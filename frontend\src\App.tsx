import { Routes, Route, Link, useLocation } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";

// Page Components
function Home() {
  const [count, setCount] = useState(0);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="text-center"
    >
      <h1 className="text-4xl font-bold text-gray-800 mb-8">
        Welcome to React + Tailwind + Framer Motion
      </h1>
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => setCount(count + 1)}
          className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200"
        >
          Count is {count}
        </motion.button>
        <p className="mt-4 text-gray-600">
          Click the button to see Framer Motion animations!
        </p>
      </div>
    </motion.div>
  );
}

function About() {
  return (
    <motion.div
      initial={{ opacity: 0, x: 100 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -100 }}
      transition={{ duration: 0.3 }}
      className="text-center"
    >
      <h1 className="text-4xl font-bold text-gray-800 mb-8">About Page</h1>
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-2xl mx-auto">
        <p className="text-gray-600 mb-4">
          This is a demonstration of three powerful technologies working
          together:
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
          <motion.div
            whileHover={{ y: -5 }}
            className="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-6 rounded-lg"
          >
            <h3 className="font-bold text-lg mb-2">React Router</h3>
            <p className="text-sm">
              Client-side routing for single-page applications
            </p>
          </motion.div>
          <motion.div
            whileHover={{ y: -5 }}
            className="bg-gradient-to-br from-green-500 to-green-600 text-white p-6 rounded-lg"
          >
            <h3 className="font-bold text-lg mb-2">Tailwind CSS</h3>
            <p className="text-sm">
              Utility-first CSS framework for rapid UI development
            </p>
          </motion.div>
          <motion.div
            whileHover={{ y: -5 }}
            className="bg-gradient-to-br from-purple-500 to-purple-600 text-white p-6 rounded-lg"
          >
            <h3 className="font-bold text-lg mb-2">Framer Motion</h3>
            <p className="text-sm">Production-ready motion library for React</p>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}

function Contact() {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.3 }}
      className="text-center"
    >
      <h1 className="text-4xl font-bold text-gray-800 mb-8">Contact Us</h1>
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
        <form className="space-y-4">
          <motion.div
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            <input
              type="text"
              placeholder="Your Name"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </motion.div>
          <motion.div
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <input
              type="email"
              placeholder="Your Email"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </motion.div>
          <motion.div
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <textarea
              placeholder="Your Message"
              rows={4}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </motion.div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            type="submit"
            className="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-200"
          >
            Send Message
          </motion.button>
        </form>
      </div>
    </motion.div>
  );
}

// Navigation Component
function Navigation() {
  return (
    <nav className="bg-white shadow-lg mb-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          <Link to="/" className="text-xl font-bold text-gray-800">
            My App
          </Link>
          <div className="flex space-x-6">
            <Link
              to="/"
              className="text-gray-600 hover:text-blue-500 transition-colors duration-200"
            >
              Home
            </Link>
            <Link
              to="/about"
              className="text-gray-600 hover:text-blue-500 transition-colors duration-200"
            >
              About
            </Link>
            <Link
              to="/contact"
              className="text-gray-600 hover:text-blue-500 transition-colors duration-200"
            >
              Contact
            </Link>
          </div>
        </div>
      </div>
    </nav>
  );
}

// Main App Component
function App() {
  const location = useLocation();

  return (
    <div className="min-h-screen bg-gray-100">
      <Navigation />
      <div className="max-w-6xl mx-auto px-4 py-8">
        <AnimatePresence mode="wait">
          <Routes location={location} key={location.pathname}>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
            <Route path="/contact" element={<Contact />} />
          </Routes>
        </AnimatePresence>
      </div>
    </div>
  );
}

export default App;
